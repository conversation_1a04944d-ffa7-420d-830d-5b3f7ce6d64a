/* Mobile-specific optimizations */

/* Prevent zoom on input focus */
input,
select,
textarea {
	font-size: 16px !important;
}

/* Mobile app container */
.mobile-app-container {
	min-height: 100vh;
	min-height: 100dvh; /* Dynamic viewport height for mobile */
	overflow-x: hidden;
	position: relative;
}

/* Mobile body optimizations */
.mobile-body {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	overscroll-behavior: none;
	overflow-x: hidden;
}

/* Allow text selection for specific elements */
.mobile-body p,
.mobile-body span,
.mobile-body h1,
.mobile-body h2,
.mobile-body h3,
.mobile-body h4,
.mobile-body h5,
.mobile-body h6 {
	-webkit-user-select: text;
	-moz-user-select: text;
	-ms-user-select: text;
	user-select: text;
}

/* Touch-friendly buttons */
.mobile-button {
	min-height: 44px;
	min-width: 44px;
	touch-action: manipulation;
	-webkit-tap-highlight-color: transparent;
}

/* Camera interface optimizations */
.mobile-camera-container {
	position: relative;
	width: 100%;
	height: 100%;
	background: #000;
	overflow: hidden;
}

.mobile-camera-video {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transform: scaleX(-1); /* Mirror front camera */
}

.mobile-camera-video.back-camera {
	transform: none; /* Don't mirror back camera */
}

/* Map optimizations for mobile */
.mobile-map-container {
	position: relative;
	width: 100%;
	height: 100%;
	touch-action: pan-x pan-y;
}

.mobile-map-container .leaflet-container {
	font-size: 14px;
}

.mobile-map-container .leaflet-control-zoom {
	border: none;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.mobile-map-container .leaflet-control-zoom a {
	width: 40px;
	height: 40px;
	line-height: 40px;
	font-size: 18px;
	font-weight: bold;
}

/* Custom marker styles */
.custom-marker {
	background: transparent !important;
	border: none !important;
}

/* Loading animations optimized for mobile */
.mobile-loading-spinner {
	animation: mobile-spin 1s linear infinite;
}

@keyframes mobile-spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* Smooth transitions for mobile */
.mobile-transition {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Safe area handling for devices with notches */
.mobile-safe-area {
	padding-top: env(safe-area-inset-top);
	padding-bottom: env(safe-area-inset-bottom);
	padding-left: env(safe-area-inset-left);
	padding-right: env(safe-area-inset-right);
}

/* Dropdown optimizations for mobile */
.mobile-dropdown {
	max-height: 50vh;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
}

.mobile-dropdown-item {
	min-height: 48px;
	display: flex;
	align-items: center;
	padding: 12px 16px;
	touch-action: manipulation;
}

/* Card optimizations */
.mobile-card {
	border-radius: 12px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

/* Button press feedback */
.mobile-button:active {
	transform: scale(0.98);
	transition: transform 0.1s ease;
}

/* Prevent pull-to-refresh */
body {
	overscroll-behavior-y: contain;
}

/* Hide scrollbars on mobile */
.mobile-scrollable {
	scrollbar-width: none;
	-ms-overflow-style: none;
}

.mobile-scrollable::-webkit-scrollbar {
	display: none;
}

/* Optimize text rendering */
.mobile-text {
	text-rendering: optimizeLegibility;
	-webkit-font-feature-settings: 'kern' 1;
	font-feature-settings: 'kern' 1;
}

/* Camera countdown animation */
.mobile-countdown {
	animation: mobile-pulse 1s ease-in-out infinite;
}

@keyframes mobile-pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.1);
		opacity: 0.8;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

/* Gesture handling */
.mobile-gesture-area {
	touch-action: pan-x pan-y pinch-zoom;
}

/* Performance optimizations */
.mobile-gpu-accelerated {
	transform: translateZ(0);
	-webkit-transform: translateZ(0);
	will-change: transform;
}

/* Dark mode support for mobile */
@media (prefers-color-scheme: dark) {
	.mobile-app-container {
		background-color: #0f172a;
		color: #f8fafc;
	}
}

/* Landscape orientation adjustments */
@media screen and (orientation: landscape) and (max-height: 500px) {
	.mobile-header {
		padding: 8px 16px;
	}

	.mobile-content {
		padding: 12px 16px;
	}
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
	.mobile-icon {
		image-rendering: -webkit-optimize-contrast;
		image-rendering: crisp-edges;
	}
}

/* Accessibility improvements for mobile */
@media (prefers-reduced-motion: reduce) {
	.mobile-transition,
	.mobile-loading-spinner,
	.mobile-countdown {
		animation: none;
		transition: none;
	}
}

/* Focus styles for keyboard navigation */
.mobile-focusable:focus {
	outline: 2px solid #3b82f6;
	outline-offset: 2px;
}

/* Haptic feedback simulation */
.mobile-haptic:active {
	animation: mobile-haptic-feedback 0.1s ease;
}

@keyframes mobile-haptic-feedback {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(0.95);
	}
	100% {
		transform: scale(1);
	}
}

/* Network status indicator */
.mobile-network-indicator {
	position: fixed;
	top: env(safe-area-inset-top, 0);
	left: 0;
	right: 0;
	z-index: 9999;
	background: #ef4444;
	color: white;
	text-align: center;
	padding: 8px;
	font-size: 14px;
	transform: translateY(-100%);
	transition: transform 0.3s ease;
}

.mobile-network-indicator.show {
	transform: translateY(0);
}

/* Battery optimization classes */
.mobile-low-power .mobile-transition {
	transition: none;
}

.mobile-low-power .mobile-loading-spinner {
	animation-duration: 2s;
}

/* Additional mobile optimizations */
.mobile-swipe-area {
	touch-action: pan-y;
	-webkit-overflow-scrolling: touch;
}

/* Pull-to-refresh prevention */
.mobile-no-pull-refresh {
	overscroll-behavior-y: contain;
	-webkit-overscroll-behavior-y: contain;
}

/* Improved button touch targets */
.mobile-touch-target {
	min-height: 44px;
	min-width: 44px;
	position: relative;
}

.mobile-touch-target::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	min-height: 44px;
	min-width: 44px;
	z-index: -1;
}

/* Camera specific optimizations */
.mobile-camera-fullscreen {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	background: #000;
}

/* Map touch optimizations */
.mobile-map-touch {
	touch-action: pan-x pan-y pinch-zoom;
}

/* Prevent text selection on UI elements */
.mobile-no-select {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-touch-callout: none;
}

/* Allow text selection on content */
.mobile-allow-select {
	-webkit-user-select: text;
	-moz-user-select: text;
	-ms-user-select: text;
	user-select: text;
}

/* Improved focus indicators for accessibility */
.mobile-focus-visible:focus-visible {
	outline: 2px solid #3b82f6;
	outline-offset: 2px;
	border-radius: 4px;
}

/* Loading state optimizations */
.mobile-loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10000;
	backdrop-filter: blur(2px);
}

/* Improved toast positioning for mobile */
.mobile-toast-container {
	position: fixed;
	top: env(safe-area-inset-top, 20px);
	left: 16px;
	right: 16px;
	z-index: 10001;
}

/* Vibration feedback simulation */
@keyframes mobile-vibrate {
	0%,
	100% {
		transform: translateX(0);
	}
	25% {
		transform: translateX(-2px);
	}
	75% {
		transform: translateX(2px);
	}
}

.mobile-vibrate {
	animation: mobile-vibrate 0.1s ease-in-out;
}
