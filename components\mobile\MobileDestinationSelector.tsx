'use client';

import { useState } from 'react';
import { ChevronDown, MapPin, Navigation } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface MobileDestinationSelectorProps {
  destinations: string[];
  onDestinationSelect: (destination: string) => void;
}

export default function MobileDestinationSelector({
  destinations,
  onDestinationSelect,
}: MobileDestinationSelectorProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedDestination, setSelectedDestination] = useState<string>('');

  const handleDestinationClick = (destination: string) => {
    setSelectedDestination(destination);
    setIsDropdownOpen(false);
  };

  const handleContinue = () => {
    if (selectedDestination) {
      onDestinationSelect(selectedDestination);
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Card */}
      <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <CardHeader className="text-center pb-4">
          <div className="mx-auto w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4">
            <Navigation className="w-8 h-8" />
          </div>
          <CardTitle className="text-2xl font-bold">Welcome to C-TRACK</CardTitle>
          <CardDescription className="text-blue-100">
            AI-powered campus navigation system
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Destination Selection */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <MapPin className="w-5 h-5 mr-2 text-blue-600" />
            Select Your Destination
          </CardTitle>
          <CardDescription>
            Choose where you want to go on campus
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Custom Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="w-full p-4 text-left bg-gray-50 border border-gray-200 rounded-lg flex items-center justify-between hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <span className={selectedDestination ? 'text-gray-900' : 'text-gray-500'}>
                {selectedDestination || 'Choose destination...'}
              </span>
              <ChevronDown 
                className={`w-5 h-5 text-gray-400 transition-transform ${
                  isDropdownOpen ? 'rotate-180' : ''
                }`} 
              />
            </button>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
                {destinations.map((destination, index) => (
                  <button
                    key={index}
                    onClick={() => handleDestinationClick(destination)}
                    className="w-full p-4 text-left hover:bg-blue-50 transition-colors border-b border-gray-100 last:border-b-0 focus:outline-none focus:bg-blue-50"
                  >
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-3 text-blue-600" />
                      <span className="text-gray-900">{destination}</span>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Continue Button */}
          <Button
            onClick={handleContinue}
            disabled={!selectedDestination}
            className="w-full h-12 text-lg font-semibold bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:text-gray-500"
          >
            Continue to Camera
          </Button>
        </CardContent>
      </Card>

      {/* Instructions Card */}
      <Card className="border-0 shadow-lg bg-gradient-to-r from-green-50 to-green-100">
        <CardContent className="p-6">
          <h3 className="font-semibold text-green-800 mb-3 flex items-center">
            <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center mr-2">
              <span className="text-white text-sm font-bold">1</span>
            </div>
            How it works
          </h3>
          <div className="space-y-2 text-sm text-green-700">
            <div className="flex items-start">
              <div className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span>Select your destination from the dropdown above</span>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span>Use the camera to capture your current location</span>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span>AI + GPS will determine your exact position</span>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span>Get turn-by-turn navigation to your destination</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features Grid */}
      <div className="grid grid-cols-2 gap-4">
        <Card className="border-0 shadow-md bg-purple-50">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-white text-xl">🤖</span>
            </div>
            <h4 className="font-semibold text-purple-800 text-sm">AI Detection</h4>
            <p className="text-purple-600 text-xs mt-1">VGG16 model for accurate location identification</p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md bg-orange-50">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-white text-xl">📍</span>
            </div>
            <h4 className="font-semibold text-orange-800 text-sm">GPS Fusion</h4>
            <p className="text-orange-600 text-xs mt-1">60% AI + 40% GPS for precise positioning</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
