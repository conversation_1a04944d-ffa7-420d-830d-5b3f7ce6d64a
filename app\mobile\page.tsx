'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { useToast } from '@/hooks/use-toast';
import { campusLocations } from '@/lib/campus-data';
import {
  getCurrentGPSLocation,
  adaptiveFuseAIAndGPS,
  GPSLocation,
  LocationData as GPSLocationData
} from '@/lib/mobile-gps';
import { useNetworkStatus } from '@/hooks/use-mobile-optimizations';
import { useHapticFeedback, useMobileViewport } from '@/hooks/use-mobile-gestures';

// Dynamically import mobile components to avoid SSR issues
const MobileDestinationSelector = dynamic(() => import('@/components/mobile/MobileDestinationSelector'), { ssr: false });
const MobileCameraInterface = dynamic(() => import('@/components/mobile/MobileCameraInterface'), { ssr: false });
const MobileNavigationMap = dynamic(() => import('@/components/mobile/MobileNavigationMap'), { ssr: false });

type MobileAppState = 'destination' | 'camera' | 'detecting' | 'navigation';

interface LocationData {
  name: string;
  confidence: number;
  lat?: number;
  lng?: number;
}

export default function MobilePage() {
  const [appState, setAppState] = useState<MobileAppState>('destination');
  const [selectedDestination, setSelectedDestination] = useState<string>('');
  const [detectedSource, setDetectedSource] = useState<LocationData | null>(null);
  const [gpsLocation, setGpsLocation] = useState<GPSLocation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { isOnline, showNetworkIndicator, isSlowConnection } = useNetworkStatus();
  const { success, warning } = useHapticFeedback();
  const { preventZoom } = useMobileViewport();

  // Get GPS location on component mount and setup mobile optimizations
  useEffect(() => {
    getCurrentLocation();
    preventZoom(); // Prevent accidental zoom on mobile
  }, [getCurrentLocation, preventZoom]);

  const getCurrentLocation = async () => {
    try {
      const location = await getCurrentGPSLocation();
      setGpsLocation(location);
      toast({
        title: 'GPS Active',
        description: `Location accuracy: ${Math.round(location.accuracy)}m`,
      });
    } catch (error) {
      console.error('GPS Error:', error);
      toast({
        title: 'GPS Error',
        description: error instanceof Error ? error.message : 'Unable to get your current location.',
        variant: 'destructive',
      });
    }
  };

  const handleDestinationSelect = (destination: string) => {
    setSelectedDestination(destination);
    setAppState('camera');
    success(); // Haptic feedback for successful selection
  };

  const handleLocationDetected = async (aiLocation: string, confidence: number) => {
    setIsLoading(true);
    setAppState('detecting');

    try {
      // Use adaptive fusion logic: 60% AI + 40% GPS (with dynamic adjustment)
      const fusionResult = await adaptiveFuseAIAndGPS(aiLocation, confidence, gpsLocation);

      setDetectedSource(fusionResult.location);
      setAppState('navigation');

      toast({
        title: 'Location Detected!',
        description: `Source: ${fusionResult.location.name} (${Math.round(fusionResult.location.confidence * 100)}% confidence) - ${fusionResult.location.source.toUpperCase()}`,
      });
      success(); // Haptic feedback for successful detection
    } catch (error) {
      console.error('Location fusion error:', error);
      toast({
        title: 'Detection Failed',
        description: 'Failed to determine your location. Please try again.',
        variant: 'destructive',
      });
      warning(); // Haptic feedback for error
      setAppState('camera');
    } finally {
      setIsLoading(false);
    }
  };



  const handleBackToDestination = () => {
    setAppState('destination');
    setDetectedSource(null);
    setSelectedDestination('');
  };

  const handleRetakePhoto = () => {
    setAppState('camera');
    setDetectedSource(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-blue-100 flex flex-col mobile-app-container mobile-no-pull-refresh mobile-no-select">
      {/* Network Status Indicator */}
      {showNetworkIndicator && (
        <div className={`mobile-network-indicator ${showNetworkIndicator ? 'show' : ''}`}>
          {!isOnline ? 'No internet connection' : isSlowConnection ? 'Slow connection detected' : 'Connection restored'}
        </div>
      )}

      {/* Mobile Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 p-4 mobile-safe-area">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-900">C-TRACK Mobile</h1>
          <div className="flex items-center space-x-2">
            {gpsLocation && (
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            )}
            <span className="text-sm text-gray-600">
              {gpsLocation ? 'GPS Active' : 'GPS Inactive'}
            </span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 p-4">
        {appState === 'destination' && (
          <MobileDestinationSelector
            onDestinationSelect={handleDestinationSelect}
            destinations={campusLocations.map(loc => loc.name)}
          />
        )}

        {appState === 'camera' && (
          <MobileCameraInterface
            destination={selectedDestination}
            onLocationDetected={handleLocationDetected}
            onBack={handleBackToDestination}
          />
        )}

        {appState === 'detecting' && (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Detecting Source</h2>
            <p className="text-gray-600 text-center">
              Analyzing image and GPS data to determine your location...
            </p>
          </div>
        )}

        {appState === 'navigation' && detectedSource && (
          <MobileNavigationMap
            sourceLocation={detectedSource.name}
            destinationLocation={selectedDestination}
            onRetakePhoto={handleRetakePhoto}
            onBackToDestination={handleBackToDestination}
          />
        )}
      </main>
    </div>
  );
}
